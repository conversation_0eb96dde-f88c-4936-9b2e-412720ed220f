package dreamboard

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/dsoplabs/dinbora-backend/internal/service/gamification"

	_dreamboard "github.com/dsoplabs/dinbora-backend/internal/repository/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/repository/financialsheet"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// DreamboardDTO represents the response structure for API backward compatibility
// This DTO assembles data from multiple collections (dreamboards, dreams, categories)
// to maintain the same API response format as the previous embedded structure
type DreamboardDTO struct {
	ObjectID        primitive.ObjectID     `json:"-" bson:"_id,omitempty"`
	ID              string                 `json:"id,omitempty" bson:"-"`
	User            string                 `json:"user" bson:"user"`
	Categories      []*dreamboard.Category `json:"categories" bson:"categories"`
	Dreams          []*dreamboard.Dream    `json:"dreams" bson:"dreams"`
	TotalDreamsCost monetary.Amount        `json:"totalDreamsCost" bson:"totalDreamsCost"`
	SavedAmount     monetary.Amount        `json:"savedAmount"`
	MonthlyNeeded   monetary.Amount        `json:"monthlyNeeded" bson:"monthlyNeeded"`
	RemainingAmount monetary.Amount        `json:"remainingAmount" bson:"remainingAmount"`
	CreatedAt       time.Time              `json:"createdAt" bson:"createdAt"`
	UpdatedAt       time.Time              `json:"updatedAt" bson:"updatedAt"`
}

// FromDreamboard creates a DreamboardDTO from a Dreamboard and associated data
func (dto *DreamboardDTO) FromDreamboard(dreamboard *dreamboard.Dreamboard, dreams []*dreamboard.Dream, categories []*dreamboard.Category) {
	dto.ObjectID = dreamboard.ObjectID
	dto.ID = dreamboard.ID
	dto.User = dreamboard.User
	dto.Categories = categories
	dto.Dreams = dreams
	dto.TotalDreamsCost = dreamboard.TotalDreamsCost
	dto.SavedAmount = dreamboard.SavedAmount
	dto.MonthlyNeeded = dreamboard.MonthlyNeeded
	dto.RemainingAmount = dreamboard.RemainingAmount
	dto.CreatedAt = dreamboard.CreatedAt
	dto.UpdatedAt = dreamboard.UpdatedAt
}

// ComputeTotals calculates and updates the total costs and savings fields for the DTO
func (dto *DreamboardDTO) ComputeTotals(dreams []*dreamboard.Dream) {
	var totalCost, monthlySum monetary.Amount
	for _, dream := range dreams {
		// Only consider active dreams
		if !dream.Completed {
			totalCost += dream.EstimatedCost
			monthlySum += dream.MonthlySavings
		}
	}
	dto.TotalDreamsCost = totalCost
	dto.MonthlyNeeded = monthlySum
	dto.RemainingAmount = dto.SavedAmount - dto.TotalDreamsCost
}

type Service interface {
	// CRUD
	Create(ctx context.Context, board *dreamboard.Dreamboard) (string, error)
	Find(ctx context.Context, id string) (*dreamboard.Dreamboard, error)
	FindAll(ctx context.Context) ([]*dreamboard.Dreamboard, error)
	FindAllByUsers(ctx context.Context, userIDs []string) ([]*dreamboard.Dreamboard, error)
	FindByUser(ctx context.Context, userID string) (*dreamboard.Dreamboard, error)
	Update(ctx context.Context, board *dreamboard.Dreamboard) error
	Delete(ctx context.Context, id string) error

	// Category CRUD
	CreateCategory(ctx context.Context, board *dreamboard.Dreamboard, category *dreamboard.Category) (*dreamboard.Category, error)
	FindCategory(ctx context.Context, board *dreamboard.Dreamboard, categoryID string) (*dreamboard.Category, error)
	FindCategories(ctx context.Context) ([]*dreamboard.Category, error)
	UpdateCategory(ctx context.Context, board *dreamboard.Dreamboard, category *dreamboard.Category) (*dreamboard.Category, error)
	DeleteCategory(ctx context.Context, board *dreamboard.Dreamboard, categoryID string) error

	// Dream CRUD
	CreateDream(ctx context.Context, board *dreamboard.Dreamboard, dream *dreamboard.Dream) (*CreateDreamResponseDTO, error)
	FindDream(ctx context.Context, board *dreamboard.Dreamboard, dreamID string) (*dreamboard.Dream, error)
	FindDreamsByDreamboardID(ctx context.Context, dreamboardID primitive.ObjectID) ([]*dreamboard.Dream, error)
	FindPersonalDreams(ctx context.Context, userID string) ([]*dreamboard.Dream, error)
	FindSharedDreams(ctx context.Context, userID string) ([]*dreamboard.Dream, error)
	FindDreamDetails(ctx context.Context, dreamID string, userID string) (*DreamDetails, error)
	UpdateDream(ctx context.Context, board *dreamboard.Dreamboard, dream *dreamboard.Dream) ([]*dreamboard.Dream, error)
	RemoveDream(ctx context.Context, board *dreamboard.Dreamboard, dream *dreamboard.Dream) ([]*dreamboard.Dream, error)

	// Dream Management
	CalculateDreamDuration(ctx context.Context, dreamID string, estimatedCost monetary.Amount) (*int, error)

	// Invitation Management
	InviteDetails(ctx context.Context, code string) (*InviteDetailsDTO, error)
	JoinSharedDream(ctx context.Context, token string, userID string, isCreator bool, monthlyPledgedAmount monetary.Amount) (*dreamboard.Contribution, error)

	// Share Link Management
	CreateShareLink(ctx context.Context, dreamID string) (*dreamboard.ShareLink, error)
	FindShareLink(ctx context.Context, dreamID string) (*dreamboard.ShareLink, error)
	FindShareLinkByCode(ctx context.Context, code string) (*dreamboard.ShareLink, error)
	UpdateShareLinkStatus(ctx context.Context, dreamID string, isEnabled bool) error
	RegenerateShareLink(ctx context.Context, dreamID string) (*dreamboard.ShareLink, error)

	// Contribution Management
	CreateContribution(ctx context.Context, contribution *dreamboard.Contribution) (*dreamboard.Contribution, error)
	FindContributionsByDreamID(ctx context.Context, dreamID string) ([]*dreamboard.Contribution, error)
	FindActiveContributionsByDreamID(ctx context.Context, dreamID string) ([]*dreamboard.Contribution, error)
	FindContributionsByUserID(ctx context.Context, userID string) ([]*dreamboard.Contribution, error)
	FindContributionByDreamAndUser(ctx context.Context, dreamID, userID string) (*dreamboard.Contribution, error)
	UpdateContribution(ctx context.Context, contribution *dreamboard.Contribution) error
	UpdateContributionStatus(ctx context.Context, dreamID string, status dreamboard.ContributionStatus) error
	DeleteContribution(ctx context.Context, contributionID string) error

	// Utility
	Initialize(ctx context.Context, userID string) error

	// Internal service-to-service methods (for backward compatibility with other services)
	GetDreamboardWithDreamsAndCategories(ctx context.Context, userID string) (*DreamboardDTO, error)
}

type service struct {
	Repository               _dreamboard.Repository
	FinancialSheetRepository financialsheet.Repository
	GamificationService      gamification.Service
}

func New(repository _dreamboard.Repository, financialSheetRepository financialsheet.Repository, gamificationService gamification.Service) Service {
	return &service{
		Repository:               repository,
		FinancialSheetRepository: financialSheetRepository,
		GamificationService:      gamificationService,
	}
}

// CRUD
func (s *service) Create(ctx context.Context, board *dreamboard.Dreamboard) (string, error) {
	currentTime := time.Now()
	board.CreatedAt = currentTime
	board.UpdatedAt = currentTime
	// Initialize computed fields to zero since there are no dreams yet
	board.TotalDreamsCost = 0
	board.SavedAmount = 0
	board.MonthlyNeeded = 0
	board.RemainingAmount = 0

	dreamboardID, err := s.Repository.Create(ctx, board)
	if err != nil {
		return "", err
	}

	return dreamboardID, err
}

func (s *service) Find(ctx context.Context, id string) (*dreamboard.Dreamboard, error) {
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.NewWithTranslationKey(errors.Service, "invalid dreamboard id", errors.KeyDreamboardErrorInvalidId, errors.BadRequest, err)
	}

	board, err := s.Repository.Find(ctx, objID)
	if err != nil {
		return nil, err
	}

	// Set ID for response
	if board != nil && !board.ObjectID.IsZero() {
		board.ID = board.ObjectID.Hex()
	}

	return board, nil
}

func (s *service) FindAll(ctx context.Context) ([]*dreamboard.Dreamboard, error) {
	boards, err := s.Repository.FindAll(ctx)
	if err != nil {
		return nil, err
	}

	// Set IDs for response
	for _, board := range boards {
		if board != nil && !board.ObjectID.IsZero() {
			board.ID = board.ObjectID.Hex()
		}
	}

	return boards, nil
}

func (s *service) FindAllByUsers(ctx context.Context, userIDs []string) ([]*dreamboard.Dreamboard, error) {
	boards, err := s.Repository.FindAllByUsers(ctx, userIDs)
	if err != nil {
		return nil, err
	}

	// Set IDs for response
	for _, board := range boards {
		if board != nil && !board.ObjectID.IsZero() {
			board.ID = board.ObjectID.Hex()
		}
	}

	return boards, nil
}

func (s *service) FindByUser(ctx context.Context, userID string) (*dreamboard.Dreamboard, error) {
	board, err := s.Repository.FindByUser(ctx, userID)
	if err != nil {
		if err.(*errors.DomainError).Kind() != errors.NotFound {
			return nil, err
		}
		// Initialize new dreamboard for user
		if err := s.Initialize(ctx, userID); err != nil {
			return nil, err
		}

		// Fetch the newly created dreamboard
		board, err = s.Repository.FindByUser(ctx, userID)
		if err != nil {
			return nil, err
		}
	}

	// Set ID for response
	if board != nil && !board.ObjectID.IsZero() {
		board.ID = board.ObjectID.Hex()
	}

	return board, nil
}

func (s *service) Update(ctx context.Context, board *dreamboard.Dreamboard) error {
	if err := board.Validate(); err != nil {
		return err
	}

	if board.ObjectID.IsZero() {
		objID, err := primitive.ObjectIDFromHex(board.ID)
		if err != nil {
			return errors.NewValidationError(errors.Service, "invalid dreamboard ID", errors.KeyDreamboardErrorInvalidId, err)
		}
		board.ObjectID = objID
	}

	board.UpdatedAt = time.Now()

	// Get dreams to compute totals
	dreams, err := s.Repository.FindActiveDreamsByDreamboardID(ctx, board.ObjectID)
	if err != nil {
		return err
	}

	board.ComputeTotals(dreams) // Update computed fields before saving

	err = s.Repository.Update(ctx, board)
	if err != nil {
		return err
	}

	return nil
}

func (s *service) Delete(ctx context.Context, id string) error {
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.NewWithTranslationKey(errors.Service, "invalid dreamboard ID", errors.KeyDreamboardErrorInvalidId, errors.BadRequest, err)
	}

	return s.Repository.Delete(ctx, objID)
}

// Utility
func (s *service) Initialize(ctx context.Context, userID string) error {
	existing, err := s.Repository.FindByUser(ctx, userID)
	if err != nil && err.(*errors.DomainError).Kind() != errors.NotFound {
		return err
	}

	if existing != nil {
		return errors.NewConflictError(errors.Service, "dreamboard already exists", errors.KeyDreamboardErrorAlreadyExists, nil)
	}

	currentTime := time.Now()
	newBoard := &dreamboard.Dreamboard{
		User:      userID,
		CreatedAt: currentTime,
		UpdatedAt: currentTime,
		// Initialize computed fields to zero since there are no dreams
		TotalDreamsCost: 0,
		SavedAmount:     0,
		MonthlyNeeded:   0,
		RemainingAmount: 0,
	}

	if err := newBoard.Validate(); err != nil {
		return err
	}

	_, err = s.Repository.Create(ctx, newBoard)
	if err != nil {
		return err
	}

	// Add default categories
	categories := []*dreamboard.Category{
		&dreamboard.Category{Identifier: dreamboard.Professional.String(), Name: "Professional"},
		&dreamboard.Category{Identifier: dreamboard.Financial.String(), Name: "Financial"},
		&dreamboard.Category{Identifier: dreamboard.Leisure.String(), Name: "Leisure"},
		&dreamboard.Category{Identifier: dreamboard.Emotional.String(), Name: "Emotional"},
		&dreamboard.Category{Identifier: dreamboard.Intellectual.String(), Name: "Intellectual"},
		&dreamboard.Category{Identifier: dreamboard.Spiritual.String(), Name: "Spiritual"},
		&dreamboard.Category{Identifier: dreamboard.Physical.String(), Name: "Physical"},
		&dreamboard.Category{Identifier: dreamboard.Intimate.String(), Name: "Intimate"},
		&dreamboard.Category{Identifier: dreamboard.Social.String(), Name: "Social"},
		&dreamboard.Category{Identifier: dreamboard.Familial.String(), Name: "Familial"},
	}

	return s.Repository.CreateCategories(ctx, categories)
}

// GetDreamboardWithDreamsAndCategories assembles dreamboard with dreams and categories for service-to-service calls
func (s *service) GetDreamboardWithDreamsAndCategories(ctx context.Context, userID string) (*DreamboardDTO, error) {
	// Get dreamboard
	board, err := s.FindByUser(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Get dreams for this dreamboard
	dreams, err := s.Repository.FindDreamsByDreamboardID(ctx, board.ObjectID)
	if err != nil {
		return nil, err
	}

	// Get all categories
	categories, err := s.Repository.FindCategories(ctx)
	if err != nil {
		return nil, err
	}

	// Get financial sheet for calculations
	financialsheet, err := s.FinancialSheetRepository.FindByUser(ctx, board.User)
	if err != nil {
		return nil, err
	}

	// Create DTO
	dto := &DreamboardDTO{}
	dto.FromDreamboard(board, dreams, categories)

	// Set IDs for response
	if dto.ObjectID.IsZero() == false {
		dto.ID = dto.ObjectID.Hex()
	}

	for _, dream := range dto.Dreams {
		if dream != nil && !dream.ObjectID.IsZero() {
			dream.ID = dream.ObjectID.Hex()
		}
	}

	// Update computed fields
	board.CalculateSavedAmount(financialsheet, dreams)
	dto.SavedAmount = board.SavedAmount
	dto.ComputeTotals(dto.Dreams)

	return dto, nil
}
