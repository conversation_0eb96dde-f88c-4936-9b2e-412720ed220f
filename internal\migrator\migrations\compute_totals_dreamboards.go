package migrations

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// ComputeTotalsDreamboards implements the Migration interface for creating default dreamboards
type ComputeTotalsDreamboards struct {
	db *mongo.Database
}

func NewComputeTotals(db *mongo.Database) *ComputeTotalsDreamboards {
	return &ComputeTotalsDreamboards{db: db}
}

func (m *ComputeTotalsDreamboards) Name() string {
	return "compute_totals_dreamboards"
}

func (m *ComputeTotalsDreamboards) Up(ctx context.Context) error {
	dreamboardCollection := m.db.Collection(repository.DREAMBOARDS_COLLECTION)

	cursor, err := dreamboardCollection.Find(ctx, bson.D{})
	if err != nil {
		return err
	}
	defer cursor.Close(ctx)

	for cursor.Next(ctx) {
		var board dreamboard.Dreamboard
		if err := cursor.Decode(&board); err != nil {
			return err
		}

		// Get dreams for this dreamboard from the dreams collection
		dreamsCollection := m.db.Collection("dreams")
		dreamsCursor, err := dreamsCollection.Find(ctx, bson.M{"dreamboardId": board.ObjectID})
		if err != nil {
			return err
		}
		defer dreamsCursor.Close(ctx)

		var dreams []*dreamboard.Dream
		for dreamsCursor.Next(ctx) {
			var dream dreamboard.Dream
			if err := dreamsCursor.Decode(&dream); err != nil {
				return err
			}
			dreams = append(dreams, &dream)
		}

		board.ComputeTotals(dreams)

		result, err := dreamboardCollection.UpdateOne(ctx,
			bson.D{primitive.E{Key: "_id", Value: board.ObjectID}},
			bson.M{"$set": bson.M{
				"totalDreamsCost": board.TotalDreamsCost,
				"savedAmount":     board.SavedAmount,
				"monthlyNeeded":   board.MonthlyNeeded,
				"remainingAmount": board.RemainingAmount,
			}},
		)

		if err != nil {
			if mongo.IsDuplicateKeyError(err) {
				return errors.New(errors.Migrator, errors.DreamboardConflictUpdate, errors.Conflict, err)
			}
			return errors.New(errors.Migrator, errors.DreamboardUpdateFailed, errors.Internal, err)
		}

		if result.MatchedCount == 0 {
			return errors.New(errors.Migrator, errors.DreamboardNotFound, errors.NotFound, nil)
		}
	}

	return cursor.Err()
}
